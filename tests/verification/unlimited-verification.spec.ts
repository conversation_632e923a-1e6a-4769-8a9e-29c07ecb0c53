import { SumsubVerificationScreen } from '../../support/pages/widgets/SumsubVerificationScreen';
import { VerificationPage } from '../../support/pages/verification/VerificationPage';
import { test } from '../../support/fixtures/user.fixture';
import { VerificationTier } from '../../support/types';
import { expect } from '@playwright/test';

test.describe('Unlimited verification', { tag: ['@verification'] }, () => {
  test.describe('User with scale verification', () => {
    test('Should show Sumsub iframe', async ({ verifiedUser }) => {
      const { page } = await verifiedUser(VerificationTier.Scale);
      const verificationPage = new VerificationPage(page);
      await verificationPage.goto();
      await verificationPage.availableVerificationScreen.clickUpgradeVerification();
      const frame = new SumsubVerificationScreen(page).getSumsubIframe();

      await expect(frame.locator('body')).toBeVisible();
      await expect(frame.getByRole('button', { name: 'Continue' })).toBeVisible();
      // We dont test the full flow here, just check that iframe is loaded
    });
  });
});
