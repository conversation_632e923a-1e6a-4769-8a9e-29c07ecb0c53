import { DashboardPage } from './../../support/pages/DashboardPage';
import { SubscriptionDetailPage } from './../../support/pages/subscribtion/SubscriptionDetailPage';
import { SubscriptionPromoPage } from '../../support/pages/subscribtion/SubscriptionPromoPage';
import { expect, test } from '../../support/fixtures/user.fixture';
import { SubscriptionTierTitle, VerificationTier } from '../../support/types';
import { performSubscription } from '../../support/reusable/commands/subscription/subscription';
import { waitForNetworkIdle } from '../../support/helpers/wait-for-network-idle';

test.describe('Subscription', { tag: ['@subscription'] }, () => {
  test.describe('With user with positive balance', () => {
    test('Should be able to add subscription', async ({ positiveUser }) => {
      const { page } = await positiveUser(VerificationTier.Scale);

      await new DashboardPage(page).leftMenuWidget.clickMenuItemByUrl('subscription');

      const subscriptionPage = new SubscriptionPromoPage(page);
      await subscriptionPage.selectTariffByName(SubscriptionTierTitle.ExtraSmall);

      const modal = subscriptionPage.subscriptionBuyModal;
      await modal.connectAccountBtn.click();
      await modal.purchaseCompleteTakeLaterButton.click();

      const subscriptionDetailPage = new SubscriptionDetailPage(page);
      await subscriptionDetailPage.goto();

      const currentTariffTitle = await subscriptionDetailPage.getCurrentTariffTitle();
      expect(currentTariffTitle.toLowerCase()).toEqual(SubscriptionTierTitle.ExtraSmall.toLowerCase());
    });
  });

  test.describe('With user with subscription', () => {
    test.beforeEach(async ({ positiveUser }) => {
      const { page } = await positiveUser(VerificationTier.Scale);
      await new SubscriptionPromoPage(page).goto();
      await performSubscription(page, SubscriptionTierTitle.ExtraSmall);
    });

    test('Should be able to cancel subscription', async ({ page }) => {
      const subscriptionDetailPage = new SubscriptionDetailPage(page);
      await subscriptionDetailPage.goto();
      await subscriptionDetailPage.cancelSubscriptionReason(0);

      const dashboardPage = new DashboardPage(page);
      await dashboardPage.goto();
      await waitForNetworkIdle(page);
      await expect(dashboardPage.subscriptionBlock).toBeHidden();
    });

    test('Should be able to switch subscription plan', async ({ page }) => {
      const subscriptionDetailPage = new SubscriptionDetailPage(page);
      await subscriptionDetailPage.goto();
      await subscriptionDetailPage.manageSubscriptionButton.click();
      await subscriptionDetailPage.upgradeTariffButton.click();
      await subscriptionDetailPage.selectTariffByName(SubscriptionTierTitle.Small);
    });
  });
});
