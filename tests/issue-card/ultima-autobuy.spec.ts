import { UltimaWithoutSubscriptionCardIssueStep } from '../../support/pages/widgets/issue-card/steps/UltimaWithoutSubscriptionCardIssueStep';
import { CountrySetOne } from '../../support/reusable/commands/verification/country-select';
import { performWelcomeVerification } from '../../support/reusable/commands/verification/welcome-verification';
import { AutoBuyPaymentStep } from '../../support/pages/widgets/issue-card/steps/AutoBuyPaymentStep';
import { haveCorrectTotalCalculated } from '../../support/reusable/shared-examples/issue-card/have-correct-total-calculated';
import { performAutobuy } from '../../support/reusable/commands/issue-card/steps/autobuy';
import { waitForNetworkIdle } from '../../support/helpers/wait-for-network-idle';
import { CreateCardPage } from '../../support/pages/create-card/CreateCardPage';
import { paymentCreate } from '../../support/helpers/emulate-api';
import { DashboardPage } from '../../support/pages/DashboardPage';
import { testUser } from '../../support/helpers/random-helpers';
import { test } from '../../support/fixtures/user.fixture';
import { Settings } from '../../support/settings';
import { expect } from '@playwright/test';

test.describe('Issue card with autobuy', { tag: ['@issue-card'] }, () => {
  test.setTimeout(Settings.timeouts.long);
  const { lastName, firstName, birthDate } = testUser;

  const paymentSystems = [
    { system: 'Mastercard', value: 0 },
    { system: 'Visa', value: 1 },
  ];

  paymentSystems.forEach(({ system, value }) => {
    test.describe(`With payment system: ${system}`, () => {
      const periods = [
        { name: 'Weekly', period: 0 },
        { name: 'Monthly', period: 1 },
        { name: 'Annually', period: 2 },
      ];
      periods.forEach(({ name, period }) => {
        test(`Should issue card with ${name} tariff`, async ({ loggedInUser }) => {
          const { page } = loggedInUser;
          await expect(page).toHaveURL(/.*dashboard.*/);
          const dashboardPage = new DashboardPage(page);
          let createCardPage = new CreateCardPage(page);

          await test.step('Click issue card', async () => {
            await expect(dashboardPage.issueUltimaCardButton).toBeVisible();
            await expect(dashboardPage.issueUltimaCardButton).toBeEnabled();
            await dashboardPage.issueUltimaCardButton.click();
          });

          await test.step('Complete welcome verification', async () => {
            await performWelcomeVerification(
              firstName,
              lastName,
              birthDate,
              CountrySetOne.AL,
              page
            );
          });

          await test.step('Card issue step', async () => {
            const issueCardStep = new UltimaWithoutSubscriptionCardIssueStep(page);
            await issueCardStep.selectCardTariff(period);
            await issueCardStep.selectPaymentSystem(value);

            await haveCorrectTotalCalculated(page);

            await issueCardStep.checkAgreement();
            await issueCardStep.clickContinue();
          });

          await test.step('Complete autobuy step', async () => {
            await performAutobuy(page);
          });

          await test.step('Complete card issue', async () => {
            await createCardPage.clickContinue();
            await createCardPage.clickConfirm();

            await expect(page).toHaveURL(/.*dashboard.*/);
          });
        });
      });
    });
  });

  test.describe('Variations', { tag: ['@variations'] }, () => {
    test.setTimeout(Settings.timeouts.fiveMinutes / 2);
    test.beforeEach(async ({ loggedInUser }) => {
      const { page } = loggedInUser;
      await expect(page).toHaveURL(/.*dashboard.*/);
      const issueCardButton = page.getByTestId('issue-ultima-card-button');
      await issueCardButton.click();
      await performWelcomeVerification(firstName, lastName, birthDate, CountrySetOne.AL, page);
    });

    // starting balance buttons
    test('starting balance buttons', async ({ page }) => {
      const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

      for (const { order, value } of [
        { order: 0, value: '50.00' },
        { order: 1, value: '100.00' },
        { order: 2, value: '250.00' },
        { order: 3, value: '500.00' },
      ]) {
        await issueCardPage.clickStartBalance(order);
        const startingBalanceInput = await issueCardPage.getStartingBalanceInput();
        await expect(startingBalanceInput).toHaveValue(value);
      }
    });

    // without agreement checkbox
    test('With not checked agreement checkbox: it should show red label', async ({
      loggedInUser,
    }) => {
      const { page } = loggedInUser;
      const issueCardStep = new UltimaWithoutSubscriptionCardIssueStep(page);

      await issueCardStep.clickContinue();
      const agreementCheckbox = issueCardStep.page.locator(issueCardStep.AGREEMENT_CHECKBOX);
      const agreementCheckboxLabel = agreementCheckbox.locator('+ *');

      // assert text color is red
      await expect(agreementCheckboxLabel).toHaveCSS('color', 'rgb(255, 71, 71)');
    });

    //TODO: refactor later, extract and get from API
    const welcomeTransferLimit = 500;
    test('With transfer more than requested, but less than welcome limit. It should issue card.', async ({
      loggedInUser,
    }) => {
      const { page } = loggedInUser;
      const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

      await issueCardPage.clickStartBalance(1);

      await haveCorrectTotalCalculated(page);

      await issueCardPage.checkAgreement();
      await issueCardPage.clickContinue();

      // autobuy
      const autoBuyStep = new AutoBuyPaymentStep(page);
      const address = await autoBuyStep.getWalletAddress();

      await paymentCreate(address, String(welcomeTransferLimit - 1));

      const createCardPage = new CreateCardPage(page);

      await createCardPage.clickContinue();
      await createCardPage.clickConfirm();

      await expect(page).toHaveURL(/.*dashboard.*/);

      const dashboardPage = new DashboardPage(page);
      const cards = dashboardPage.page.getByTestId(dashboardPage.CARD_ITEM_ID);
      await expect(cards).toHaveCount(1);
    });

    // with transfer more than requested and more than welcome limit
    test('With transfer more than requested and more than welcome limit. It should not issue card.', async ({
      loggedInUser,
    }) => {
      const { page } = loggedInUser;
      const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

      await issueCardPage.clickStartBalance(1);
      await issueCardPage.checkAgreement();
      await issueCardPage.clickContinue();

      // autobuy
      const autoBuyStep = new AutoBuyPaymentStep(page);
      const address = await autoBuyStep.getWalletAddress();

      await paymentCreate(address, String(welcomeTransferLimit * 2));

      const createCardPage = new CreateCardPage(page);
      await createCardPage.clickContinue();

      await waitForNetworkIdle(page);

      await expect(page).toHaveURL(/.*dashboard.*/);
      const dashboardPage = new DashboardPage(page);

      const cards = dashboardPage.page.getByTestId(dashboardPage.CARD_ITEM_ID);
      await expect(cards).toHaveCount(0);
    });

    // with transfer less than requested
    test('Transfer less than requested', async ({ loggedInUser }) => {
      const { page } = loggedInUser;
      const issueCardPage = new UltimaWithoutSubscriptionCardIssueStep(page);

      await issueCardPage.clickStartBalance(1);
      await issueCardPage.checkAgreement();
      await issueCardPage.clickContinue();

      // autobuy
      const autoBuyStep = new AutoBuyPaymentStep(page);
      const address = await autoBuyStep.getWalletAddress();
      const amount = await autoBuyStep.getTotalTopUp();
      const transferAmount = String(Number(amount) / 2);

      await paymentCreate(address, transferAmount);

      const createCardPage = new CreateCardPage(page);
      await createCardPage.clickContinue();
      await createCardPage.clickConfirm();

      await expect(page).toHaveURL(/.*dashboard.*/);
      const dashboardPage = new DashboardPage(page);
      const cards = dashboardPage.page.getByTestId(dashboardPage.CARD_ITEM_ID);
      await expect(cards).toHaveCount(1);
    });
  });
});
