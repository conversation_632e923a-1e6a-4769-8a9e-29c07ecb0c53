import { UltimaWithoutSubscriptionCardIssueStep } from '../../support/pages/widgets/issue-card/steps/UltimaWithoutSubscriptionCardIssueStep';
import { CardApproveStep } from '../../support/pages/widgets/issue-card/steps/CardApproveStep';
import { haveCorrectTotalCalculated } from '../../support/reusable/shared-examples/issue-card/have-correct-total-calculated';
import { CreateCardPage } from '../../support/pages/create-card/CreateCardPage';
import { TCurrencyAccountSelect, VerificationTier } from '../../support/types';
import { DashboardPage } from '../../support/pages/DashboardPage';
import { test } from '../../support/fixtures/user.fixture';
import { expect } from '@playwright/test';

test.describe('Issue Ultima Card', { tag: ['@issue-card'] }, () => {
  test.describe('With user with scale verification and positive balance', () => {
    test.beforeEach(async ({ positiveUser }) => {
      const { page } = await positiveUser(VerificationTier.Scale);
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.clickIssueUltimaCard();
    });

    const paymentSystems = [
      { name: 'Mastercard', value: 0 },
      { name: 'Visa', value: 1 },
    ];

    paymentSystems.forEach(({ name: paymentSystem, value }) => {
      test.describe(`With ${paymentSystem}`, () => {
        const periods = [
          { name: 'Weekly', period: 0 },
          { name: 'Monthly', period: 1 },
          { name: 'Annually', period: 2 },
        ];

        periods.forEach(({ name, period }) => {
          test.describe(`With ${name} tariff`, () => {
            test(`Should issue card with ${paymentSystem} and with ${name} tariff`, async ({
              page,
            }) => {
              await test.step('Card params', async () => {
                const issueCardStep = new UltimaWithoutSubscriptionCardIssueStep(page);
                await issueCardStep.selectCardTariff(period);
                await issueCardStep.selectPaymentSystem(value);
                await issueCardStep.fillStartBalance('5000');

                // with BTC
                await issueCardStep.selectAccount(TCurrencyAccountSelect.BTC);
                await haveCorrectTotalCalculated(page, TCurrencyAccountSelect.BTC);

                // with USDT
                await issueCardStep.selectAccount(TCurrencyAccountSelect.USDT);
                await haveCorrectTotalCalculated(page, TCurrencyAccountSelect.USDT);

                await issueCardStep.checkAgreement();
                await issueCardStep.clickContinue();
              });

              await test.step('Approve card', async () => {
                const approveStep = new CardApproveStep(page);
                await expect(approveStep.confirmButton).toBeEnabled();
                await approveStep.confirmButton.click();
              });

              await test.step('Confirm issue card', async () => {
                const createCardPage = new CreateCardPage(page);
                await createCardPage.clickContinue();
                await createCardPage.clickConfirm();
              });

              await test.step('Check card in dashboard', async () => {
                const dashboardPage = new DashboardPage(page);
                await expect(page).toHaveURL(/.*dashboard.*/);

                // check card in dashboard
                const cards = dashboardPage.page.getByTestId(dashboardPage.CARD_ITEM_ID);
                await expect(cards).toHaveCount(1);

                const card = cards.first();
                const autoRefillIcon = card.getByTestId(dashboardPage.CARD_AUTO_REFILL_ICON_ID_ON);
                await expect(autoRefillIcon).toBeVisible();
              });
            });
          });
        });
      });
    });
  });
});
