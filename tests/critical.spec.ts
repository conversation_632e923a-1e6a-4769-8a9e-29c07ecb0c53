import { waitForNetworkIdle } from '../support/helpers/wait-for-network-idle';
import { TCurrencyAccountSelect, VerificationTier } from '../support/types';
import { DepositModal } from '../support/pages/widgets/DepositModal';
import { expect, test } from '../support/fixtures/user.fixture';
import { DashboardPage } from '../support/pages/DashboardPage';
import { paymentCreate } from '../support/helpers/emulate-api';

test.describe('With logged in user', () => {
  test('Should be able to top-up balance', async ({ verifiedUser }) => {
    const { page } = await verifiedUser(VerificationTier.Welcome);
    const dashboardPage = new DashboardPage(page);

    await test.step('Click top-up button', async () => {
      await dashboardPage.accountsWidget.clickTopUpButton();
    });

    let address: string;

    await test.step('Copy address', async () => {
      const depositModal = new DepositModal(page);
      address = await depositModal.getCryptoDepositAddress();
      expect(address).not.toBeNull();
    });

    await test.step('Create payment with API', async () => {
      await paymentCreate(address, '100');
    });

    await test.step('Check account balance', async () => {
      const dashboardPage = new DashboardPage(page);
      await dashboardPage.closeSidebar();
      await waitForNetworkIdle(page);
      const usdtBalance = await dashboardPage.accountsWidget.getAccountBalanceInUSD(
        TCurrencyAccountSelect.USDT
      );
      expect(usdtBalance).toBeGreaterThan(0);
    });
  });
});
