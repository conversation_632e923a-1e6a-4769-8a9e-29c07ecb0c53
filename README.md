# Dashboard autotest

Dashboard playwright autotests.

## Installationasd
- `npm install`

## Run
```bash
npm run test
```
run with UI
```bash
npm run test:ui
```

run debug
```bash
npm run test:debug
```

show report
```bash
npm run report
```


## Env variables

BASE_URL - url of the dashboard
EMULATE_API_URL - url of the emulate api
BACKEND_API_URL - url of the backend api


> https://emulate.pstage.net/api/documentation#/