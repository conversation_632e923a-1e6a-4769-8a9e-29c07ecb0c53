import { randomEmail } from './random-helpers';
import { Settings } from '../settings';

export const apiUserRegister = async (email = '', password = '') => {
  const userEmail = email.length > 0 ? email : randomEmail();
  const userPassword = password.length > 0 ? password : 'password';
  const url = Settings.backendApiUrl + '/user/register';

  const res = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify({
      email: userEmail,
      password: userPassword,
    }),
  });
  return await res.json();
};
