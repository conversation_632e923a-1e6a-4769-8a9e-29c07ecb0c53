import { Page } from '@playwright/test';

/**
 * Waits for network idle state.
 */
export const waitForNetworkIdle = async (
  page: Page,
  timeout: number = 3000,
  checkInterval: number = 300
) => {
  let lastActivity = Date.now();

  const updateLastActivity = () => {
    lastActivity = Date.now();
  };

  page.on('request', updateLastActivity);
  page.on('requestfinished', updateLastActivity);
  page.on('requestfailed', updateLastActivity);

  return new Promise<void>((resolve) => {
    const interval = setInterval(() => {
      if (Date.now() - lastActivity > timeout) {
        clearInterval(interval);
        resolve();
      }
    }, checkInterval);
  });
};
