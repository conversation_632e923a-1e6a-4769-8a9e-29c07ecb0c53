import { VerificationTier } from '../types';
import { Settings } from '../settings';

export const paymentCreate = async (
  address: string,
  amount: string = Settings.defaultAmount.toString()
) => {
  const createPaymentUrl = Settings.emulateApiUrl + '/payment/create';

  return await fetch(createPaymentUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify({
      address,
      confirmations: 10,
      ticker: 'USDT',
      amount,
    }),
  });
};

export type TCreateUserPayload = {
  user: {
    email: string;
    password: string;
    master_id?: number;
    type?: number;
  };
  verification: {
    slug: VerificationTier;
    first_name?: string;
    last_name?: string;
    birthday?: string;
    country_iso3?: string;
  };
  country_set?: {
    id: number;
  };
  is_suspicious?: boolean;
  show_warn?: boolean;
};

export const createUserEmulate = async (payload: TCreateUserPayload) => {
  const createUserUrl = Settings.emulateApiUrl + '/user-register/site';

  return await fetch(createUserUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(payload),
  });
};
