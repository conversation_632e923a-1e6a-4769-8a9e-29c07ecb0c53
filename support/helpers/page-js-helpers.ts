import { ExchangeRatesResponse, TCurrency, TCurrencyAccountSelect } from '../types';
import { Page } from '@playwright/test';
import { Settings } from '../settings';

export const getUserToken = async (page: Page) => {
  return await page.evaluate(() => {
    return localStorage.getItem('auth._token.local');
  });
};

export const getExchangeRates = async (
  page: Page,
  currency: TCurrency = TCurrencyAccountSelect.USDT
) => {
  const url = Settings.backendApiUrl + '/user/exchange-rates';
  const res = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${await getUserToken(page)}`,
    },
  });
  const response: ExchangeRatesResponse = await res.json();
  const { data: rates } = response;
  return Number(rates[currency]['USD'] || '1');
};
