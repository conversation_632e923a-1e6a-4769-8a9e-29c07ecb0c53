import { DashboardPage } from '../../../pages/DashboardPage';
import { LoginPage } from '../../../pages/LoginPage';
import { expect, Page } from '@playwright/test';

export const performLogin = async (email: string, password: string, page: Page) => {
  const loginPage = new LoginPage(page);
  await loginPage.goto();
  await loginPage.continueWithEmail();
  await loginPage.fillEmail(email);
  await loginPage.fillPassword(password);
  await loginPage.submit();
  await expect(page).toHaveURL(/.*dashboard.*/);
  return new DashboardPage(page);
};
