import { DashboardPage } from '../../../pages/DashboardPage';
import { RegisterPage } from '../../../pages/RegisterPage';
import { expect, Page } from '@playwright/test';

export const performRegistration = async (email: string, password: string, page: Page) => {
  const registerPage = new RegisterPage(page);
  await registerPage.goto();
  await registerPage.continueWithEmail();
  await registerPage.fillEmail(email);
  await registerPage.fillPassword(password);
  await registerPage.fillConfirmPassword(password);
  await registerPage.checkAgreements();
  await registerPage.submit();
  await expect(page).toHaveURL(/.*dashboard.*/);
  return new DashboardPage(page);
};
