import { expect, Page } from '@playwright/test';
import { DashboardPage } from '../../../pages/DashboardPage';
import { SubscriptionPromoPage } from '../../../pages/subscribtion/SubscriptionPromoPage';
import { SubscriptionTierTitle } from '../../../types';

export const performSubscription = async (
  page: Page,
  tariffName: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall
) => {
  const subscriptionPage = new SubscriptionPromoPage(page);
  await subscriptionPage.goto();
  await subscriptionPage.selectTariffByName(tariffName);

  const modal = subscriptionPage.subscriptionBuyModal;
  await modal.connectAccountBtn.click();
  await modal.purchaseCompleteTakeLaterButton.click();
  await new DashboardPage(page).goto();
  await expect(page).toHaveURL(/.*dashboard.*/);
};
