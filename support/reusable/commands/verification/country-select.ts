import { Page } from '@playwright/test';
import { VerificationPage } from '../../../pages/verification/VerificationPage';

/*
Set1 - доступны верификация всех трех уровней - welcome, scale, unlimited
Set2 - доступны верификация только уровней - scale, unlimited
Set3 - доступны верификация только уровня - unlimited

!Counties can be switched between sets by the backend without notice!

Set1:
- Albania - 2
- Angola - 6
- Azerbaijan - 8
- Argentina - 9


Set2:
- Andorra - 5
- American Samoa - 4
- Australia - 10
- Austria - 11

Set3:
- Afghanistan - 1

*/

export enum CountrySetOne {
  // Albania
  AL = 2,
  // Angola
  AO = 6,
  // Azerbaijan
  AZ = 8,
  // Argentina
  AR = 9,
}

export enum CountrySetTwo {
  // Andorra
  AD = 5,
  // American Samoa
  AS = 4,
  // Australia
  AU = 10,
  // Austria
  AT = 11,
}

export enum CountrySetThree {
  // Afghanistan
  AF = 1,
}

export type CountrySetSelection = CountrySetOne | CountrySetTwo | CountrySetThree;

export const performCountrySelect = async (
  country: CountrySetSelection,
  page: Page,
  withContinue = false
) => {
  const countryIndex = country - 1;
  const countrySelectInput = page
    .getByTestId('country-select-input')
    .describe('Country select input');
  await countrySelectInput.click();

  const secondOption = page.getByTestId('country-select-item').nth(countryIndex);
  await secondOption.click();
  if (withContinue) {
    const verificationPage = new VerificationPage(page);
    await verificationPage.countrySelectContinueButton.click();
  }
};
