import { AvailableVerificationWidget } from '../widgets/AvailableVerificationWidget';
import { Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class VerificationPage extends BasePage {
  path = '/app/settings/verification';

  verificationApproved = this.page.getByTestId('verification-approved');
  countrySelectContinueButton = this.page.getByTestId('country-select-continue-button');

  get availableVerificationScreen() {
    return new AvailableVerificationWidget(this.page);
  }

  constructor(page: Page) {
    super(page);
  }
}
