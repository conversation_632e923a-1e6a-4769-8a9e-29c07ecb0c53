import { Page } from '@playwright/test';
import { Widget } from '../Widget';

export class CreateCardSummary extends Widget {
  ID = 'create-card-summary';

  monthlyPaymentValue = this.getWidget().getByTestId('create-card-summary-period');
  startingBalanceContainer = this.getWidget().getByTestId('create-card-summary-start-balance');
  totalContainer = this.getWidget().getByTestId('create-card-summary-total');
  exchangeRateValue = this.getWidget().getByTestId('exchange-rate-value');
  topUpFee = this.getWidget().getByTestId('create-card-summary-top-up-fee');

  constructor(page: Page) {
    super(page);
  }

  getWidget() {
    return this.page.getByTestId(this.ID);
  }

  async getMonthlyPaymentValue() {
    const value = await this.monthlyPaymentValue.textContent();
    return value ? parseFloat(value.replace(/[^0-9.]/g, '')) : 0;
  }

  async getStartingBalance() {
    const value = await this.startingBalanceContainer.textContent();
    return value ? parseFloat(value.replace(/[^0-9.]/g, '')) : 0;
  }

  async getTopUpFee() {
    const value = await this.topUpFee.locator('> div').last().textContent();

    const match = value?.match(/([0-9.]+)%/);
    return match ? parseFloat(match[1]) / 100 : 0;
  }

  async getTotal() {
    const value = await this.totalContainer.textContent();
    return value ? parseFloat(value.replace(/[^0-9.]/g, '')) : 0;
  }

  async getExchangeRate(): Promise<number> {
    if (!(await this.exchangeRateValue.isVisible())) {
      return 1; // No exchange rate element means no conversion needed
    }
    const value = await this.exchangeRateValue.textContent();

    const match = value?.match(/=\s*\$?([0-9.]+)/);
    return match ? parseFloat(match[1]) : 0;
  }
}
