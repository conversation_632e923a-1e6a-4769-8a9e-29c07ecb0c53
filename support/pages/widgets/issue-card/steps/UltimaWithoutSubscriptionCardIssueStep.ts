import { CurrencyAccountSelectMap, TCurrency, TCurrencyAccountSelect } from '../../../../types';
import { CreateCardSummary } from '../CreateCardSummary';
import { Page } from '@playwright/test';
import { Widget } from '../../Widget';

export class UltimaWithoutSubscriptionCardIssueStep extends Widget {
  AGREEMENT_CHECKBOX = '[data-cy="agreement"]';
  CONTINUE_BUTTON = '[data-cy="order_button"]';
  SELECT_PAYMENT_SYSTEM_INPUT_ID = 'select-payment-system-input';
  SELECT_PAYMENT_SYSTEM_OPTION_ID = 'select-payment-system-option';
  START_BALANCE_INPUT_CONTAINER_ID = 'start-balance-input';
  START_BALANCE_BUTTON_ID = 'start-balance-button';

  ACCOUNT_SELECT_ID = 'account-select';
  ACCOUNTS_AND_CARDS_SELECT_OPTION_ACCOUNT_ID = 'accounts-and-cards-select-option-account';

  cardTariffTitles = this.page.getByTestId('ultima-tariff-title');

  get summaryWidget() {
    return new CreateCardSummary(this.page);
  }

  constructor(page: Page) {
    super(page);
  }

  async selectCardTariff(number = 0) {
    const selectedTariff = this.cardTariffTitles.nth(number);
    await selectedTariff.click();
  }

  async checkAgreement() {
    const agreementCheckbox = this.page.locator(this.AGREEMENT_CHECKBOX).locator('> span');
    await agreementCheckbox.click();
  }

  async clickContinue() {
    const continueButton = this.page.locator(this.CONTINUE_BUTTON);
    await continueButton.click();
  }

  async selectPaymentSystem(number = 0) {
    const selectPaymentSystemInput = this.page.getByTestId(this.SELECT_PAYMENT_SYSTEM_INPUT_ID);
    await selectPaymentSystemInput.click();

    const selectPaymentSystemOption = this.page
      .getByTestId(this.SELECT_PAYMENT_SYSTEM_OPTION_ID)
      .nth(number);
    await selectPaymentSystemOption.click();
  }

  async getStartingBalanceInput() {
    const startBalanceInput = this.page
      .getByTestId(this.START_BALANCE_INPUT_CONTAINER_ID)
      .locator('input');
    return startBalanceInput;
  }

  async fillStartBalance(startBalance: string) {
    const startBalanceInput = await this.getStartingBalanceInput();
    await startBalanceInput.fill(startBalance);
  }

  async clickStartBalance(number = 0) {
    const startBalanceButton = this.page.getByTestId(this.START_BALANCE_BUTTON_ID).nth(number);
    await startBalanceButton.click();
  }

  async selectAccount(order: TCurrency = TCurrencyAccountSelect.USDT) {
    const number = CurrencyAccountSelectMap[order];
    await this.page.getByTestId(this.ACCOUNT_SELECT_ID).click();
    await this.page
      .getByTestId(this.ACCOUNTS_AND_CARDS_SELECT_OPTION_ACCOUNT_ID)
      .nth(number)
      .click();
  }
}
