import { Page } from '@playwright/test';
import { Widget } from './Widget';

export class VerificationButtonWidget extends Widget {
  VERIFICATION_WIDGET_TEXT_ID = 'verification-widget-text';
  VERIFICATION_BUTTON_ID = 'verification-widget';
  verificationButton = this.page
    .getByTestId(this.VERIFICATION_BUTTON_ID)
    .describe('Verification Button');

  constructor(page: Page) {
    super(page);
    this.verificationButton = this.page
      .getByTestId(this.VERIFICATION_BUTTON_ID)
      .describe('Verification Button');
  }
}
