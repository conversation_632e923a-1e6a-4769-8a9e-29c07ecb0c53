import { waitForNetworkIdle } from '../../helpers/wait-for-network-idle';
import { Widget } from './Widget';
import { Page } from '@playwright/test';

export class LeftMenuWidget extends Widget {
  ID = 'left-menu-navigation';
  MENU_ITEM_ID = 'navigation-link';

  widget = this.page.getByTestId(this.ID);

  constructor(page: Page) {
    super(page);
  }

  async clickMenuItem(order: number) {
    const menuItem = this.widget.getByTestId(this.MENU_ITEM_ID).nth(order);
    await menuItem.click();
  }

  async clickMenuItemByUrl(urlPart: string) {
    const menuItems = this.widget.getByTestId(this.MENU_ITEM_ID);
    await waitForNetworkIdle(this.page)
    for (const menuItem of await menuItems.all()) {
      const href = await menuItem.getAttribute('href');
      if (href?.includes(urlPart)) {
        await menuItem.click();
        break;
      }
    }
  }
}
