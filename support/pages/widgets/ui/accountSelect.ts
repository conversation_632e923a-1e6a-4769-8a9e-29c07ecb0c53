import { CurrencyAccountSelectMap, TCurrencyAccountSelect } from '../../../types';
import { Widget } from '../Widget';
import { Page } from '@playwright/test';

export class AccountSelect extends Widget {
  ID = 'accounts-and-cards-select';
  OPTION_ID = 'country-select-item';

  openButton = this.page.getByTestId(this.ID).getByTestId('ui-select');

  widget = this.page.getByTestId(this.ID);

  constructor(page: Page) {
    super(page);
  }

  async selectAccount(order: TCurrencyAccountSelect = TCurrencyAccountSelect.USDT) {
    const index = CurrencyAccountSelectMap[order];
    await this.openButton.click();
    await this.widget.getByTestId(this.OPTION_ID).nth(index).click();
  }
}
