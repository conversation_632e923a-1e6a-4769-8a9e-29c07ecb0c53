import { TCurrency, TCurrencyAccountSelect } from '../../types';
import { Page } from '@playwright/test';
import { Widget } from './Widget';

export class AccountsWidget extends Widget {
  USDT_ACCOUNT_ID = 'usdt_account';
  BTC_ACCOUNT_ID = 'btc_account';
  USD_ACCOUNT_ID = 'usd_account';
  EUR_ACCOUNT_ID = 'eur_account';

  ACCOUNT_BALANCE_ID = 'account-balance';

  constructor(page: Page) {
    super(page);
  }

  async getAccountByCurrency(currency: TCurrency) {
    let accountId: string;
    switch (currency) {
      case TCurrencyAccountSelect.USDT:
        accountId = this.USDT_ACCOUNT_ID;
        break;
      case TCurrencyAccountSelect.BTC:
        accountId = this.BTC_ACCOUNT_ID;
        break;
      case TCurrencyAccountSelect.USD:
        accountId = this.USD_ACCOUNT_ID;
        break;
      case TCurrencyAccountSelect.EUR:
        accountId = this.EUR_ACCOUNT_ID;
        break;
    }

    const accountContainer = this.page.getByTestId(accountId);
    await accountContainer.waitFor({ state: 'visible' });
    return accountContainer;
  }

  async getAccountBalanceInUSD(currency: TCurrency) {
    const accountContainer = await this.getAccountByCurrency(currency);
    const balanceContainer = accountContainer.getByTestId(this.ACCOUNT_BALANCE_ID);
    await balanceContainer.waitFor({ state: 'visible' });
    const balanceText = (await balanceContainer.textContent()) ?? '';
    // $123.45 -> 123.45, ₮123.45 -> 123.45
    return parseFloat(balanceText.replace(/[^0-9.]/g, ''));
  }

  async clickTopUpButton() {
    await this.page.getByRole('button', { name: 'topup-button' }).click();
  }
}
