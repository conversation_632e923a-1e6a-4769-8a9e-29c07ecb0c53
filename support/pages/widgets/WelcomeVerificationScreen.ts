import { Page } from '@playwright/test';
import { Widget } from './Widget';

export class WelcomeVerificationScreen extends Widget {
  firstNameInput = this.page.getByTestId('first-name-input').describe('first name input');
  lastNameInput = this.page.getByTestId('last-name-input').describe('last name input');
  birthDateInput = this.page.getByTestId('birth-date-input').describe('birth date input');
  confirmButtonContainer = this.page.getByTestId('experimental-check-failed-message');

  countrySelectContinueButton = this.page.getByTestId('country-select-continue-button');

  unsupportedRegionMessage = this.page.getByTestId('unsupported-country-message');

  constructor(page: Page) {
    super(page);
  }

  async fillFirstName(firstName: string) {
    const input = this.firstNameInput.locator('input').describe('first name input');
    await input.fill(firstName);
  }

  async fillLastName(lastName: string) {
    const input = this.lastNameInput.locator('input').describe('last name input');
    await input.fill(lastName);
  }

  async fillBirthDate(birthDate: string) {
    const input = this.birthDateInput.locator('input').describe('birth date input');
    await input.fill(birthDate);
  }

  async clickContinue() {
    const continueButton = this.countrySelectContinueButton;
    await continueButton.click();
  }

  async clickConfirm(): Promise<boolean> {
    const shownConfirmContainer = await this.confirmButtonContainer
      .waitFor({
        state: 'visible',
        timeout: 3000,
      })
      .then(() => true)
      .catch(() => false);

    if (shownConfirmContainer) {
      const confirmButton = this.confirmButtonContainer
        .getByRole('button')
        .describe('confirm button');
      await confirmButton.click();
      return true;
    } else {
      return false;
    }
  }
}
