import { Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class CreateCardPage extends BasePage {
  path = '/app/create';

  createCardSuccessButton = this.page.getByTestId('create-card-success-button');
  createCardConfirmButton = this.page.getByTestId('create-card-confirm-button');

  constructor(page: Page) {
    super(page);
  }

  async clickContinue() {
    await this.createCardSuccessButton.click();
  }

  async clickConfirm() {
    await this.createCardConfirmButton.click();
  }
}
