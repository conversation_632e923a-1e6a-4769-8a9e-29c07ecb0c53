import { Page } from '@playwright/test';
import { DashboardPage } from '../DashboardPage';
import { SubscriptionTierTitle } from '../../types';

export class SubscriptionDetailPage extends DashboardPage {
  path = '/app/subscription/detail';

  currentPlanBlock = this.page.getByTestId('current-plan-block');
  manageSubscriptionButton = this.page.getByTestId('manage-subscription-btn');

  // subscription management sidebar (we keep it in here for now)
  dotsVerticalButton = this.page.getByTestId('dots-vertical-icon');
  cancelSubscriptionButton = this.page.getByTestId('cancel-subscription-btn');
  cancelSubscriptionButtonLosePrivate = this.page.getByTestId(
    'cancel-subscription-button-lose-private'
  );
  cancelSubscriptionButtonAnyWay = this.page.getByTestId('cancel-subscription-anyway-button');

  // upgrade
  upgradeTariffButton = this.page.getByTestId('upgrade-tariff-btn');

  // tariffs cards (with name and button)
  SUBSCRIPTION_TARIFF_CARD_ID = 'subscriptions-tariff-card';
  TARIFF_NAME_ID = 'tariff-name';
  SELECT_TARIFF_BUTTON_ID = 'select-tariff-button';

  constructor(page: Page) {
    super(page);
  }

  async getCurrentTariffTitle() {
    const text =
      (await this.currentPlanBlock.getByTestId('private-block-content').textContent()) ?? '';
    return text.trim();
  }

  async cancelSubscriptionReason(reasonIndex = 0) {
    await this.manageSubscriptionButton.click();
    await this.dotsVerticalButton.click();
    await this.cancelSubscriptionButton.click();
    await this.cancelSubscriptionButtonLosePrivate.click();
    await this.cancelSubscriptionButtonAnyWay.click();
    const options = this.page.getByTestId('subscription-cancel-option').describe('Reason options');
    const optionsCount = await options.count();
    if (optionsCount <= 0) {
      throw new Error(`Could not find any reason options`);
    }
    const tooExpensive = options.nth(reasonIndex).locator('label');
    await tooExpensive.waitFor({ state: 'visible' });
    await tooExpensive.click();
    const submitButton = this.page.getByTestId('submit-feedback-button');
    await submitButton.click();
    const doneButton = this.page.getByTestId('subscription-cancel-done-button');
    await doneButton.click();
  }

  async getTariffCards() {
    return this.page.getByTestId(this.SUBSCRIPTION_TARIFF_CARD_ID);
  }

  async getTariffCardByName(tariffName: SubscriptionTierTitle = SubscriptionTierTitle.ExtraSmall) {
    return this.page
      .getByTestId(this.SUBSCRIPTION_TARIFF_CARD_ID)
      .filter({
        has: this.page.getByTestId(this.TARIFF_NAME_ID).filter({ hasText: tariffName }),
      })
      .describe(`tariff card with name ${tariffName}`);
  }

  async selectTariffByName(tariffName: SubscriptionTierTitle) {
    const card = await this.getTariffCardByName(tariffName);
    const button = card.getByTestId(this.SELECT_TARIFF_BUTTON_ID);
    await button.click();
    
  }
}
