import { Page } from '@playwright/test';
import { DashboardPage } from '../DashboardPage';
import { SubscriptionTierTitle } from '../../types';
import { SubscriptionBuyModal } from './SubscriptionBuyModal';

export class SubscriptionPromoPage extends DashboardPage {
  path = '/app/subscription/promo';

  SUBSCRIPTION_TARIFF_CARD_ID = 'subscriptions-tariff-card';
  TARIFF_NAME_IO = 'tariff-name';
  SELECT_TARIFF_BUTTON_ID = 'select-tariff-button';

  get subscriptionBuyModal() {
    return new SubscriptionBuyModal(this.page);
  }

  constructor(page: Page) {
    super(page);
  }

  getTariffCardByName(tariffName: SubscriptionTierTitle) {
    return this.page
      .getByTestId(this.SUBSCRIPTION_TARIFF_CARD_ID)
      .filter({
        has: this.page.getByTestId(this.TARIFF_NAME_IO).filter({ hasText: tariffName }),
      })
      .describe(`tariff card with name ${tariffName}`);
  }

  async selectTariffByName(tariffName: SubscriptionTierTitle) {
    const card = this.getTariffCardByName(tariffName);
    const button = card.getByTestId(this.SELECT_TARIFF_BUTTON_ID);
    await button.click();
  }
}
