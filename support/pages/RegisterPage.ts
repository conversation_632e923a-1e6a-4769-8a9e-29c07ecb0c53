import { Page } from '@playwright/test';
import { BasePage } from './BasePage';

export class RegisterPage extends BasePage {
  path = '/register';

  emailInputContainer = this.page.locator('[data-cy="input_register_identifier"]');
  emailInput = this.page.locator('[data-cy="input_register_identifier"] input');
  passwordInput = this.page.locator('[data-cy="input_register_password"] input');
  confirmPasswordInputContainer = this.page.locator('[data-cy="input_register_confirm_password"]');
  confirmPasswordInput = this.page.locator('[data-cy="input_register_confirm_password"] input');
  continueWithEmailButton = this.page.getByTestId('continue-with-email');
  submitButton = this.page.locator('[data-cy="register_with_email_button"]');
  countryCheckbox = this.page.locator('[data-cy="agreement-country"] > div');
  privacyPolicyCheckbox = this.page.locator('[data-cy="agreement-terms"] label');


  constructor(page: Page) {
    super(page);
  }

  getEmailInputError() {
    return this.emailInputContainer.locator('>div').nth(1).locator('>span');
  }

  async continueWithEmail() {
    await this.continueWithEmailButton.click();
  }

  async fillEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async fillPassword(password: string) {
    await this.passwordInput.fill(password);
  }

  async fillConfirmPassword(password: string) {
    await this.confirmPasswordInput.fill(password);
  }

  async getConfirmPasswordInputError() {
    return this.confirmPasswordInputContainer.locator('>div').nth(1).locator('>span');
  }

  async checkAgreements() {
    await this.countryCheckbox.click();
    await this.privacyPolicyCheckbox.click();
  }

  async submit() {
    await this.submitButton.click();
  }
}
