import { VerificationButtonWidget } from './widgets/VerificationButtonWidget';
import { AccountsWidget } from './widgets/AccountsWidget';
import { Page } from '@playwright/test';
import { BasePage } from './BasePage';
import { LeftMenuWidget } from './widgets/LeftMenuWidget';

export class DashboardPage extends BasePage {
  path = '/app/dashboard';

  CARD_ITEM_ID = 'table-card-item';
  VERIFICATION_WIDGET_ICON_ID = 'verification-widget-icon';
  VERIFICATION_CIRCLE_MOBILE_ICON_ID = 'verification-circle-mobile-icon';
  USER_WIDGET_UNLIMITED_ICON_ID = 'user-widget-unlimited-icon';

  CARD_AUTO_REFILL_ICON_ID_ON = 'card-auto-refill-icon-on';
  CARD_AUTO_REFILL_ICON_ID_OFF = 'card-auto-refill-icon-off';

  issueUltimaCardButton = this.page
    .getByTestId('issue-ultima-card-button')
    .describe('issue ultima card button');

  subscriptionBlock = this.page.getByTestId('subscription-block');  

  get accountsWidget() {
    return new AccountsWidget(this.page);
  }

  get leftMenuWidget() {
    return new LeftMenuWidget(this.page);
  }

  get verificationButtonWidget() {
    return new VerificationButtonWidget(this.page);
  }

  constructor(page: Page) {
    super(page);
  }

  async goto() {
    await this.page.goto(this.path);
  }

  async clickIssueUltimaCard() {
    await this.issueUltimaCardButton.click();
  }
}
