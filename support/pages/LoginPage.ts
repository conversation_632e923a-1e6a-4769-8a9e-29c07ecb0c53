import { Page } from '@playwright/test';
import { BasePage } from './BasePage';

export class LoginPage extends BasePage {
  path = '/login';

  continueWithEmailButton = this.page.getByTestId('continue-with-email').describe('continue with email button');
  emailInput = this.page.locator('[data-cy="login_input_identifier"] input').describe('email input');
  passwordInput = this.page.locator('[data-cy="login_input_password"] input').describe('password input');
  submitButton = this.page.locator('[data-cy="login_button_login_with_email"]').describe('submit button');
  errorContainer = this.page.locator('[data-cy="error-msg"]').describe('error container');
  forgotPasswordButton = this.page.getByTestId('forgot-password-button').describe('forgot password button');

  sendLinkInput = this.page.getByTestId('send_link_input').describe('send link input');
  sendLinkSuccess = this.page.getByTestId('send_link_success').describe('send link success');
  sendLinkError = this.page.getByTestId('send_link_error').describe('send link error');
  sendLinkButton = this.page.getByTestId('send_link_button').describe('send link button');

  constructor(page: Page) {
    super(page);
  }

  async goto() {
    await this.page.goto('/login');
  }

  async continueWithEmail() {
    await this.continueWithEmailButton.click();
  }

  async fillEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async fillPassword(password: string) {
    await this.passwordInput.fill(password);
  }

  async submit() {
    await this.submitButton.click();
  }

  async openForgotPasswordModal() {
    await this.forgotPasswordButton.click();
  }

  async getLoginErrorContainer() {
    const errorContainer = this.errorContainer;
    await errorContainer.waitFor({ state: 'visible' });
    return errorContainer;
  }

  async fillRecoveryEmail(email: string) {
    await this.sendLinkInput.locator('input').fill(email);
  }
}
