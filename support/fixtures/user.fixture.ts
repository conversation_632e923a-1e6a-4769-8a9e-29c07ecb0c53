import { createUserEmulate, paymentCreate } from '../helpers/emulate-api';
import { closeTopModal } from '../reusable/commands/close-modal';
import { performLogin } from '../reusable/commands/auth/login';
import { expect, Page, test as base } from '@playwright/test';
import { DepositModal } from '../pages/widgets/DepositModal';
import { apiUserRegister } from '../helpers/backend-api';
import { randomEmail } from '../helpers/random-helpers';
import { DashboardPage } from '../pages/DashboardPage';
import { VerificationTier } from '../types';

export const test = base.extend<{
  loggedInUser: {
    page: Page;
    user: {
      email: string;
      password: string;
    };
  };
  verifiedUser: (verification: VerificationTier) => Promise<{ page: Page; email: string }>;
  positiveUser: (verification: VerificationTier) => Promise<{ page: Page; email: string }>;
}>({
  loggedInUser: async ({ page }, use) => {
    const newUser = randomEmail();
    await apiUserRegister(newUser, newUser);
    await performLogin(newUser, newUser, page);

    await use({ page, user: { email: newUser, password: newUser } });
  },
  verifiedUser: async ({ page }, use) => {
    const createVerifiedUser = async (verification: VerificationTier) => {
      const newUser = randomEmail();
      await createUserEmulate({
        user: {
          email: newUser,
          password: newUser,
        },
        verification: { slug: verification },
      });

      await performLogin(newUser, newUser, page);

      return { page, email: newUser };
    };

    await use(createVerifiedUser);
  },
  positiveUser: async ({ page }, use) => {
    const createPositiveUser = async (verification: VerificationTier = VerificationTier.Scale) => {
      const newUser = randomEmail();
      await createUserEmulate({
        user: {
          email: newUser,
          password: newUser,
        },
        verification: { slug: verification },
      });

      await performLogin(newUser, newUser, page);

      await new DashboardPage(page).accountsWidget.clickTopUpButton();
      const depositModal = new DepositModal(page);

      const address = await depositModal.getCryptoDepositAddress();
      await paymentCreate(address);

      await closeTopModal(page);
      await new DashboardPage(page).goto();
      return { page, email: newUser };
    };

    await use(createPositiveUser);
  },
});
export { expect };
