import * as dotenv from 'dotenv';

dotenv.config({ quiet: true });

// http://localhost:5173 - development
// https://vue3.pstage.net - staging

export const Settings = {
  baseUrl: process.env.BASE_URL || 'https://vue3.pstage.net',
  emulateApiUrl: process.env.EMULATE_API_URL || 'https://emulate.pstage.net/api',
  backendApiUrl: process.env.BACKEND_API_URL || 'https://api.pstage.net',
  timeouts: {
    // 5 min
    fiveMinutes: 1000 * 60 * 5,
    // 1 min
    long: 1000 * 60,
    // 30 sec
    medium: 1000 * 30,
    // 10 sec
    small: 1000 * 10,
  },
  defaultTimeout: 5000,
  defaultAmount: 1000 * 10,
};
